{"name": "thressjs-01", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc -b && vite build", "preview": "vite preview"}, "dependencies": {"gsap": "^3.13.0", "three": "^0.176.0", "vue": "^3.5.13"}, "devDependencies": {"@eslint/js": "^9.26.0", "@types/three": "^0.176.0", "@vitejs/plugin-vue": "^5.2.3", "@vue/tsconfig": "^0.7.0", "eslint": "^9.26.0", "eslint-plugin-vue": "^10.1.0", "globals": "^16.0.0", "typescript": "~5.8.3", "typescript-eslint": "^8.32.0", "vite": "^6.3.5", "vue-tsc": "^2.2.8"}}