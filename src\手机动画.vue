<template>
  <canvas ref="canvas"></canvas>
</template>

<script setup lang="ts">
  import { ref, onMounted } from 'vue';
  import * as THREE from 'three';
  import { OrbitControls } from 'three/examples/jsm/controls/OrbitControls';
  import { GUI } from 'three/examples/jsm/libs/lil-gui.module.min.js';
  // 导入hdr加载器
  import { RGBELoader } from 'three/examples/jsm/loaders/RGBELoader.js';
  // 导入gltf加载器
  import { GLTFLoader } from 'three/examples/jsm/loaders/GLTFLoader.js';
  // 导入draco解码器
  import { DRACOLoader } from 'three/examples/jsm/loaders/DRACOLoader.js';

  // 画布引用
  const canvas = ref<HTMLCanvasElement | null>(null);

  onMounted(() => {
    // 渲染器
    const renderer = new THREE.WebGLRenderer({
      canvas: canvas.value!,
      antialias: true,
    });
    // 设置渲染器大小
    renderer.setSize(window.innerWidth, window.innerHeight);

    // 场景
    const scene = new THREE.Scene();

    // 相机
    const camera = new THREE.PerspectiveCamera(45, window.innerWidth / window.innerHeight, 0.1, 1000);
    camera.position.z = 2;
    camera.position.y = 1;
    camera.position.x = 1;
    camera.lookAt(0, 0, 0);

    // 世界坐标辅助器
    const axesHelper = new THREE.AxesHelper(5);
    scene.add(axesHelper);

    // 添加网格辅助器
    const gridHelper = new THREE.GridHelper(50, 50);
    gridHelper.material.opacity = 0.3;
    gridHelper.material.transparent = true;
    scene.add(gridHelper);

    // 轨道控制器
    const controls = new OrbitControls(camera, renderer.domElement);
    controls.enableDamping = true;

    // rgbeLoader 加载hdr贴图
    let rgbeLoader = new RGBELoader();
    rgbeLoader.load('/texture/Alex_Hart-Nature_Lab_Bones_2k.hdr', envMap => {
      // 设置环境贴图的映射方式为等距矩形折射映射
      envMap.mapping = THREE.EquirectangularRefractionMapping;
      // 设置场景背景色为浅灰色
      scene.background = new THREE.Color(0xcccccc);
      // 将HDR环境贴图应用到场景环境
      scene.environment = envMap;
    });
    const gltfLoader = new GLTFLoader();
    const dracoLoader = new DRACOLoader();
    // 设置draco路径
    dracoLoader.setDecoderPath('/draco/');
    // 设置gltf加载器draco解码器
    gltfLoader.setDRACOLoader(dracoLoader);
    // 加载动画模型
    let mixer: THREE.AnimationMixer;
    gltfLoader.load('/model/huawei.glb', gltf => {
      scene.add(gltf.scene);
      //创建动画混合器
      mixer = new THREE.AnimationMixer(gltf.scene);
      //获取动画
      const action = mixer.clipAction(gltf.animations[0]);
      //播放动画
      action.play();
    });

    // lil-gui 控制面板
    const eventObj = {
      toggleFullscreen() {
        if (!document.fullscreenElement) {
          document.body.requestFullscreen();
        } else {
          document.exitFullscreen();
        }
      },
    };
    const gui = new GUI();
    gui.add(eventObj, 'toggleFullscreen').name('全屏切换');

    // 渲染函数
    let clock = new THREE.Clock();
    function animate() {
      controls.update();
      if (mixer) {
        let delta = clock.getDelta();
        mixer.update(delta);
      }
      requestAnimationFrame(animate);
      // 渲染
      renderer.render(scene, camera);
    }
    animate();

    // 监听窗口变化
    window.addEventListener('resize', () => {
      renderer.setSize(window.innerWidth, window.innerHeight);
      camera.aspect = window.innerWidth / window.innerHeight;
      camera.updateProjectionMatrix();
    });
  });
</script>

<style scoped>
  canvas {
    position: fixed;
    top: 0;
    bottom: 0;
    right: 0;
    left: 0;
    width: 100%;
    height: 100%;
  }
</style>
