<template>
  <canvas ref="canvas"></canvas>
</template>

<script setup lang="ts">
  import { ref, onMounted } from 'vue';
  import * as THREE from 'three';
  import { OrbitControls } from 'three/examples/jsm/controls/OrbitControls';
  import { GUI } from 'three/examples/jsm/libs/lil-gui.module.min.js';
  // 导入hdr加载器
  import { RGBELoader } from 'three/examples/jsm/loaders/RGBELoader.js';
  // 导入gltf加载器
  import { GLTFLoader } from 'three/examples/jsm/loaders/GLTFLoader.js';
  // 导入draco解码器
  import { DRACOLoader } from 'three/examples/jsm/loaders/DRACOLoader.js';
  // 导入变换控制器
  import { TransformControls } from 'three/examples/jsm/controls/TransformControls.js';

  // 画布引用
  const canvas = ref<HTMLCanvasElement | null>(null);

  onMounted(() => {
    // 渲染器
    const renderer = new THREE.WebGLRenderer({
      canvas: canvas.value!,
      antialias: true,
    });
    // 启用阴影渲染
    renderer.shadowMap.enabled = true;
    // 设置色调映射为Reinhard算法，用于HDR渲染
    renderer.toneMapping = THREE.ReinhardToneMapping;
    // 设置色调映射的曝光度
    renderer.toneMappingExposure = 1;
    // 设置渲染器大小
    renderer.setSize(window.innerWidth, window.innerHeight);

    // 场景
    const scene = new THREE.Scene();

    // 相机
    const camera = new THREE.PerspectiveCamera(45, window.innerWidth / window.innerHeight, 0.1, 1000);
    camera.position.z = 8;
    camera.position.y = 2.5;
    camera.position.x = 3;
    camera.lookAt(0, 1.2, 0);

    // 世界坐标辅助器
    const axesHelper = new THREE.AxesHelper(5);
    scene.add(axesHelper);

    // 添加网格辅助器
    const gridHelper = new THREE.GridHelper(50, 50);
    gridHelper.material.opacity = 0.3;
    gridHelper.material.transparent = true;
    scene.add(gridHelper);

    // 轨道控制器
    const controls = new OrbitControls(camera, renderer.domElement);
    controls.enableDamping = true;

    // 创建变换控制器
    let tControls = new TransformControls(camera, renderer.domElement);
    tControls.addEventListener('change', animate);
    // 监听拖动事件，当拖动物体时候，禁用轨道控制器
    tControls.addEventListener('dragging-changed', function (event) {
      controls.enabled = !event.value;
    });
    tControls.addEventListener('change', () => {
      if (eventObj.isClampGroup) {
        tControls.object.position.y = 0;
      }
    });
    const gizmo = tControls.getHelper();
    scene.add(gizmo);

    let basicScene: THREE.Object3D<THREE.Object3DEventMap>;
    // rgbeLoader 加载hdr贴图
    let rgbeLoader = new RGBELoader();
    rgbeLoader.load('/texture/Alex_Hart-Nature_Lab_Bones_2k.hdr', envMap => {
      // 设置环境贴图的映射方式为等距矩形折射映射
      envMap.mapping = THREE.EquirectangularRefractionMapping;
      // 设置场景背景色为浅灰色
      scene.background = new THREE.Color(0xcccccc);
      // 将HDR环境贴图应用到场景环境
      scene.environment = envMap;
    });
    const gltfLoader = new GLTFLoader();
    const dracoLoader = new DRACOLoader();
    // 设置draco路径
    dracoLoader.setDecoderPath('/draco/');
    // 设置gltf加载器draco解码器
    gltfLoader.setDRACOLoader(dracoLoader);
    // 加载基础模型
    gltfLoader.load('/model/house/house-scene-min.glb', gltf => {
      basicScene = gltf.scene;
    });

    // lil-gui 控制面板
    const eventObj = {
      toggleFullscreen() {
        if (!document.fullscreenElement) {
          document.body.requestFullscreen();
        } else {
          document.exitFullscreen();
        }
      },
      addScene: function () {
        scene.add(basicScene);
      },
      setTranslate: function () {
        tControls.setMode('translate');
      },
      setRotate: function () {
        tControls.setMode('rotate');
      },
      setScale: function () {
        tControls.setMode('scale');
      },
      toggleSpace: function () {
        tControls.setSpace(tControls.space === 'local' ? 'world' : 'local');
      },
      cancelMesh: function () {
        tControls.detach();
      },
      translateSnapNum: null as number | null,
      rotateSnapNum: 0,
      scaleSnapNum: 0,
      isClampGroup: true,
      isLight: true,
    };
    const gui = new GUI();
    gui.add(eventObj, 'toggleFullscreen').name('全屏切换');
    gui.add(eventObj, 'addScene').name('添加户型基础模型');
    gui.add(eventObj, 'setTranslate').name('设置平移');
    gui.add(eventObj, 'setRotate').name('设置旋转');
    gui.add(eventObj, 'setScale').name('设置缩放');
    gui.add(eventObj, 'toggleSpace').name('切换空间');
    gui.add(eventObj, 'cancelMesh').name('取消选中');
    gui
      .add(eventObj, 'isLight')
      .name('是否开启灯光')
      .onChange(value => {
        if (value) {
          renderer.toneMappingExposure = 1;
        } else {
          renderer.toneMappingExposure = 0.1;
        }
      });
    // 监听键盘事件
    window.addEventListener('keydown', event => {
      // 判断是否按的是t键
      if (event.key === 't') {
        eventObj.setTranslate();
      }
      if (event.key === 'r') {
        eventObj.setRotate();
      }
      if (event.key === 's') {
        eventObj.setScale();
      }
    });

    // 添加物体目录
    let meshList = [
      {
        name: '盆栽',
        path: '/model/house/plants-min.glb',
      },
      {
        name: '单人沙发',
        path: '/model/house/sofa_chair_min.glb',
      },
    ] as { name: string; path: string; addMesh: () => void }[];
    let folderAddMehs = gui.addFolder('添加物体');
    let sceneMeshes = [];
    let meshesNum: Record<string, number> = {};
    let meshesFolder = gui.addFolder('家居列表');
    meshList.forEach(item => {
      item.addMesh = function () {
        gltfLoader.load(item.path, gltf => {
          sceneMeshes.push({
            ...item,
            object3d: gltf.scene,
          });
          let object3d = gltf.scene;
          scene.add(object3d);
          tControlSelect(object3d);
          let meshOpt = {
            toggleMesh: function () {
              tControlSelect(object3d);
            },
          };
          meshesNum[item.name] = meshesNum[item.name] ? meshesNum[item.name] + 1 : 1;
          meshesFolder.add(meshOpt, 'toggleMesh').name(item.name + meshesNum[item.name]);
        });
      };
      folderAddMehs.add(item, 'addMesh').name(item.name);
    });

    function tControlSelect(mesh: THREE.Object3D<THREE.Object3DEventMap>) {
      tControls.attach(mesh);
    }

    let snapFolder = gui.addFolder('固定设置');
    snapFolder
      .add(eventObj, 'translateSnapNum', {
        '不固定': null,
        1: 1,
        0.1: 0.1,
        10: 10,
      })
      .name('固定位移设置')
      .onChange(() => {
        tControls.setTranslationSnap(eventObj.translateSnapNum);
      });
    snapFolder
      .add(eventObj, 'rotateSnapNum', 0, 1)
      .step(0.01)
      .name('旋转')
      .onChange(() => {
        tControls.setRotationSnap(eventObj.rotateSnapNum * Math.PI * 2);
      });
    snapFolder
      .add(eventObj, 'scaleSnapNum', 0, 2)
      .step(0.1)
      .name('缩放')
      .onChange(() => {
        tControls.setScaleSnap(eventObj.scaleSnapNum);
      });
    snapFolder.add(eventObj, 'isClampGroup').name('是否吸附到地面');
    // 渲染函数
    function animate() {
      controls.update();
      requestAnimationFrame(animate);
      // 渲染
      renderer.render(scene, camera);
    }
    animate();

    // 监听窗口变化
    window.addEventListener('resize', () => {
      renderer.setSize(window.innerWidth, window.innerHeight);
      camera.aspect = window.innerWidth / window.innerHeight;
      camera.updateProjectionMatrix();
    });
  });
</script>

<style scoped>
  canvas {
    position: fixed;
    top: 0;
    bottom: 0;
    right: 0;
    left: 0;
    width: 100%;
    height: 100%;
  }
</style>
